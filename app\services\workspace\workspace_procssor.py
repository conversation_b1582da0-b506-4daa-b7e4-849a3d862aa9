from app.core import ServiceResponse
from app.models.users import User
from app.services.workspace import WorkspaceService
from app.core.logger_setup import logger


# class WorkspaceProcessor:
#     """Handles validation and business logic for workspace-related APIs."""

#     @staticmethod
#     def process_get_reports_by_user_role(user: User) -> ServiceResponse:
#         """
#         Fetch reports based on the current user's role.
#         Role determines access level:
#           - Admin: sees all reports.
#           - Manager: sees reports assigned to them or their subordinates.
#           - Developer: sees only reports assigned to them.
#         """
#         return WorkspaceService.execute(WorkspaceService().get_reports_by_user_role, user)


class WorkspaceProcessor:
    """Handles validation and business logic for workspace-related APIs."""

    @staticmethod
    def process_get_reports_by_user_role(user: User) -> ServiceResponse:
        """
        Fetch reports based on the current user's role.
        Role determines access level:
          - Admin: sees all reports.
          - Manager: sees reports assigned to them or their subordinates.
          - Developer: sees only reports assigned to them.
        """
        # Get role name safely
        role_name = getattr(user, 'role_name', None)
        logger.info(f"[WorkspaceProcessor] Processing report fetch for user ID: {user.id} (Role: {role_name})")
        print(f"[DEBUG] process_get_reports_by_user_role called for user ID: {user.id}, Role: {role_name}")

        try:
            response = WorkspaceService.execute(WorkspaceService().get_reports_by_user_role, user)
            logger.info(f"[WorkspaceProcessor] Successfully fetched reports. Status: {response.status_code}")
            print(f"[DEBUG] Response status: {response.status_code}, Data count: {len(response.data) if response.data else 0}")
            return response
        except Exception as e:
            logger.error(f"[WorkspaceProcessor] Error occurred while fetching reports: {e}", exc_info=True)
            print(f"[ERROR] Exception in WorkspaceProcessor: {e}")
            return ServiceResponse.failure("Error processing report fetch by user role", 500)