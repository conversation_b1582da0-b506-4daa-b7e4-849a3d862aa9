# import json
# # from app.models_old.user import UserManager
# from app.models.users import UserManager
# from app.core import Authentication<PERSON>rror, AuthorizationError, BLOCKED_EMAILS, logger
# from fastapi import Depends, Header, HTTPException, Request
# from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
# from app.services.auth import decode_secure_jwt, decode_base
# from sqlalchemy.orm import joinedload

# security = HTTPBearer(auto_error=True)


# # async def get_current_user(
# #     authorization: str = Header(..., alias="Authorization"),
# #     encoded_email: str = Header(..., alias="X-User-Email")
# # ):
# #     try:
# #         if not authorization.startswith("Bearer "):
# #             raise AuthorizationError("Invalid Authorization header")

# #         token = authorization.split(" ")[1]
# #         email = decode_base(encoded_email)
# #         payload = json.loads(decode_secure_jwt(token, email).get("sub"))
# #         if not payload:
# #             raise AuthenticationError("Invalid token")

# #         user = UserManager.get_user_by_email(email)
# #         if not user:
    
# #             raise AuthenticationError("User not found")
# #         return user
# #     except Exception as e:
# #         raise HTTPException(status_code=e.status_code, detail=str(e.detail))
# async def get_current_user(
#     authorization: str = Header(..., alias="Authorization"),
#     encoded_email: str = Header(..., alias="X-User-Email")
# ):
#     try:
#         if not authorization.startswith("Bearer "):
#             raise AuthorizationError("Invalid Authorization header")

#         token = authorization.split(" ")[1]
#         email = decode_base(encoded_email)
#         payload = json.loads(decode_secure_jwt(token, email).get("sub"))
#         if not payload:
#             raise AuthenticationError("Invalid token")

#         # Extract role from payload
#         role_name = payload.get("role")
#         if not role_name:
#             raise AuthenticationError("Role not found in token")

#         # Fetch user with role eagerly loaded
#         user = UserManager.get_user_by_email(email, load_role=True)
#         if not user:
#             raise AuthenticationError("User not found")

#         # Attach role_name from login response to avoid accessing user.role.name
#         user.role_name = role_name
#         return user
#     except Exception as e:
#         raise HTTPException(status_code=e.status_code, detail=str(e))


# async def get_current_new_user(
#     authorization: str = Header(..., alias="Authorization"),
#     encoded_email: str = Header(..., alias="X-User-Email")
# ):
#     try:
#         if not authorization.startswith("Bearer "):
#             raise AuthorizationError("Invalid Authorization header")

#         token = authorization.split(" ")[1]
#         email = decode_base(encoded_email)
#         payload = json.loads(decode_secure_jwt(token, email).get("sub"))
#         if not payload:
#             raise AuthenticationError("Invalid token")

#         user = UserManager.get_user_by_email(email)
#         if not user:
#             raise AuthenticationError("User not found")
#         return user
#     except Exception as e:
#         raise HTTPException(status_code=e.status_code, detail=str(e.detail))


# def get_verified_user_email(
#     credentials: HTTPAuthorizationCredentials = Depends(security),
#     encoded_email: str = Header(..., alias="X-User-Email")
# ) -> str:
#     token = credentials.credentials
#     try:
#         email = decode_base(encoded_email)

#         payload = json.loads(decode_secure_jwt(token, email).get("sub"))

#         if payload.get("email") != email:
#             raise AuthenticationError("Token/email mismatch")

#         return email
#     except Exception as e:
#         raise AuthenticationError(str(e))

# async def check_blocked_email(request: Request):
#     email = request.headers.get("X-User-Email") or request.query_params.get("email")
#     if email and email in BLOCKED_EMAILS:
#         logger.warning(f"Blocked email access attempt: {email}")
#         raise AuthorizationError("Access denied. This email is blocked.")

import json
from app.models.users import UserManager
from app.core import AuthenticationError, AuthorizationError, BLOCKED_EMAILS, logger
from fastapi import Depends, Header, HTTPException, Request
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from app.services.auth import decode_secure_jwt, decode_base
from sqlalchemy.orm import joinedload

security = HTTPBearer(auto_error=True)

async def get_current_user(
    authorization: str = Header(..., alias="Authorization"),
    encoded_email: str = Header(..., alias="X-User-Email")
):
    try:
        if not authorization.startswith("Bearer "):
            raise AuthorizationError("Invalid Authorization header")

        token = authorization.split(" ")[1]
        email = decode_base(encoded_email)
        payload = json.loads(decode_secure_jwt(token, email).get("sub"))
        if not payload:
            raise AuthenticationError("Invalid token")

        # Extract role from payload
        role_name = payload.get("role")
        if not role_name:
            raise AuthenticationError("Role not found in token")

        # Fetch user with role eagerly loaded
        user = UserManager.get_user_by_email(email, load_role=True)
        if not user:
            raise AuthenticationError("User not found")

        # Attach role_name from login response
        user.role_name = role_name
        return user
    except Exception as e:
        raise HTTPException(status_code=e.status_code, detail=str(e))

async def get_current_new_user(
    authorization: str = Header(..., alias="Authorization"),
    encoded_email: str = Header(..., alias="X-User-Email")
):
    try:
        if not authorization.startswith("Bearer "):
            raise AuthorizationError("Invalid Authorization header")

        token = authorization.split(" ")[1]
        email = decode_base(encoded_email)
        payload = json.loads(decode_secure_jwt(token, email).get("sub"))
        if not payload:
            raise AuthenticationError("Invalid token")

        # Extract role from payload
        role_name = payload.get("role")
        if not role_name:
            raise AuthenticationError("Role not found in token")

        # Fetch user with role eagerly loaded
        user = UserManager.get_user_by_email(email, load_role=True)
        if not user:
            raise AuthenticationError("User not found")

        # Attach role_name from login response
        user.role_name = role_name
        return user
    except Exception as e:
        raise HTTPException(status_code=e.status_code, detail=str(e))

def get_verified_user_email(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    encoded_email: str = Header(..., alias="X-User-Email")
) -> str:
    token = credentials.credentials
    try:
        email = decode_base(encoded_email)
        payload = json.loads(decode_secure_jwt(token, email).get("sub"))

        if payload.get("email") != email:
            raise AuthenticationError("Token/email mismatch")

        return email
    except Exception as e:
        raise AuthenticationError(str(e))

async def check_blocked_email(request: Request):
    email = request.headers.get("X-User-Email") or request.query_params.get("email")
    if email and email in BLOCKED_EMAILS:
        logger.warning(f"Blocked email access attempt: {email}")
        raise AuthorizationError("Access denied. This email is blocked.")