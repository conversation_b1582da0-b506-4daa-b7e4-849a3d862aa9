from app.core import BaseService, ServiceResponse
from app.models.project_details import ProjectDetail
from app.models.report_details import ReportDetail
from sqlalchemy.orm import joinedload
from sqlalchemy import or_
from uuid import UUID
from app.models.users import User
from app.core.logger_setup import logger
from app.core.session import scoped_context


# class WorkspaceService(BaseService):
#     def get_reports_by_user_role(self, user) -> ServiceResponse:
#         with scoped_context() as session:
#             reports = ReportDetail.get_reports_by_user_role(session, user).all()

#             response_data = []
#             for report in reports:
#                 response_data.append({
#                     "report_id": str(report.report_id),
#                     "report_name": report.name,
#                     "project_name": report.project.name if report.project else None,
#                     "is_analyzed": report.is_analyzed,
#                     "analyzed_status": report.analyzed_status.name if report.analyzed_status else None,
#                     "is_converted": report.is_converted,
#                     "converted_status": report.converted_status.name if report.converted_status else None,
#                     "is_migrated": report.is_migrated,
#                     "migrated_status": report.migrated_status.name if report.migrated_status else None,
#                     "s3_location": getattr(report, "s3_location", None)
#                 })

#             print(f"user.role=====================: {user.role.name}")

#             return ServiceResponse.success(response_data)


class WorkspaceService(BaseService):
    def get_reports_by_user_role(self, user) -> ServiceResponse:
        with scoped_context() as session:
            logger.info(f"[WorkspaceService] Fetching reports for user ID: {user.id}, Role: {user.role_name}")
            print(f"[DEBUG] Fetching reports for user ID: {user.id}, Role: {user.role_name}")

            # Validate user
            if not hasattr(user, "role_name") or not user.role_name:
                logger.error("[WorkspaceService] User role not provided")
                return ServiceResponse.failure("User role not provided")

            # Fetch subordinates for Manager role
            subordinate_ids = []
            if user.role_name == "Manager":
                subordinate_ids = session.query(User.id).filter(User.manager_id == user.id).all()
                subordinate_ids = [sid[0] for sid in subordinate_ids]
                logger.info(f"[WorkspaceService] Subordinate IDs: {subordinate_ids}")
                print(f"[DEBUG] Subordinate IDs: {subordinate_ids}")

            # Build reports query
            reports_query = (
                session.query(ReportDetail)
                .join(ProjectDetail, ReportDetail.project_id == ProjectDetail.id)
                .options(joinedload(ReportDetail.project))
            )

            # Apply role-based filtering
            if user.role_name == "Admin":
                # Admin sees all reports in their organization
                reports_query = reports_query.join(ProjectDetail.creator).filter(
                    User.organization_id == user.organization_id
                )
            elif user.role_name == "Manager":
                # Manager sees reports for projects assigned to them or their subordinates
                reports_query = reports_query.filter(
                    or_(
                        ProjectDetail.assigned_to == user.id,
                        ProjectDetail.assigned_to.in_(subordinate_ids)
                    )
                ).join(ProjectDetail.creator).filter(
                    User.organization_id == user.organization_id
                )
            elif user.role_name == "Developer":
                # Developer sees reports for projects assigned to them
                reports_query = reports_query.filter(
                    ProjectDetail.assigned_to == user.id
                ).join(ProjectDetail.creator).filter(
                    User.organization_id == user.organization_id
                )
            else:
                logger.error(f"[WorkspaceService] Invalid role: {user.role_name}")
                return ServiceResponse.failure(f"Invalid role: {user.role_name}")

            # Execute query
            reports = reports_query.all()
            logger.info(f"[WorkspaceService] Found {len(reports)} reports")
            print(f"[DEBUG] Found {len(reports)} reports")

            # Format response
            response_data = []
            for report in reports:
                response_data.append({
                    "report_id": str(report.report_id),
                    "report_name": report.name,
                    "project_name": report.project.name if report.project else None,
                    "is_analyzed": report.is_analyzed,
                    "analyzed_status": report.analyzed_status.name if report.analyzed_status else None,
                    "is_converted": report.is_converted,
                    "converted_status": report.converted_status.name if report.converted_status else None,
                    "is_migrated": report.is_migrated,
                    "migrated_status": report.migrated_status.name if report.migrated_status else None,
                    "s3_location": getattr(report, "s3_location", None)
                })

            return ServiceResponse.success(response_data)