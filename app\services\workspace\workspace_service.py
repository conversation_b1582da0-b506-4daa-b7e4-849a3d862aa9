from app.core import BaseService, ServiceResponse
from app.models.project_details import ProjectDetail
from app.models.report_details import ReportDetail
from sqlalchemy.orm import joinedload
from sqlalchemy import or_
from uuid import UUID
from app.models.users import User, UserManager
from app.core.logger_setup import logger
from app.core.session import scoped_context


# class WorkspaceService(BaseService):
#     def get_reports_by_user_role(self, user) -> ServiceResponse:
#         with scoped_context() as session:
#             reports = ReportDetail.get_reports_by_user_role(session, user).all()

#             response_data = []
#             for report in reports:
#                 response_data.append({
#                     "report_id": str(report.report_id),
#                     "report_name": report.name,
#                     "project_name": report.project.name if report.project else None,
#                     "is_analyzed": report.is_analyzed,
#                     "analyzed_status": report.analyzed_status.name if report.analyzed_status else None,
#                     "is_converted": report.is_converted,
#                     "converted_status": report.converted_status.name if report.converted_status else None,
#                     "is_migrated": report.is_migrated,
#                     "migrated_status": report.migrated_status.name if report.migrated_status else None,
#                     "s3_location": getattr(report, "s3_location", None)
#                 })

#             print(f"user.role=====================: {user.role.name}")

#             return ServiceResponse.success(response_data)


class WorkspaceService(BaseService):
    def get_reports_by_user_role(self, user) -> ServiceResponse:
        with scoped_context() as session:
            try:
                logger.info(f"[WorkspaceService] Starting - Fetching reports for user ID: {user.id}")
                print(f"[DEBUG] Starting - Fetching reports for user ID: {user.id}")

                # Reload user with session-bound context to avoid DetachedInstanceError
                logger.info(f"[WorkspaceService] Reloading user with session context")
                print(f"[DEBUG] Reloading user with session context")

                session_user = UserManager.get_user_by_id_with_relations(str(user.id), session)
                if not session_user:
                    logger.error("[WorkspaceService] User not found in database")
                    print(f"[ERROR] User not found in database")
                    return ServiceResponse.failure("User not found", 404)

                logger.info(f"[WorkspaceService] User reloaded successfully: {session_user.email}")
                print(f"[DEBUG] User reloaded successfully: {session_user.email}")

                # Get role name from session-bound user
                logger.info(f"[WorkspaceService] Getting role information")
                print(f"[DEBUG] Getting role information")

                role_name = session_user.role.name.value if session_user.role and session_user.role.name else None
                if not role_name:
                    logger.error("[WorkspaceService] User role not found")
                    print(f"[ERROR] User role not found")
                    return ServiceResponse.failure("User role not found", 400)

                logger.info(f"[WorkspaceService] User role: {role_name}")
                print(f"[DEBUG] User role: {role_name}")

                # Get organization ID from session-bound user
                organization_id = session_user.organization_id
                logger.info(f"[WorkspaceService] User organization ID: {organization_id}")
                print(f"[DEBUG] User organization ID: {organization_id}")

                # Build reports query with proper joins
                reports_query = (
                    session.query(ReportDetail)
                    .join(ProjectDetail, ReportDetail.project_id == ProjectDetail.id)
                    .options(joinedload(ReportDetail.project))
                )

                # Apply role-based filtering (handle both uppercase and title case)
                role_upper = role_name.upper() if role_name else ""

                if role_upper == "ADMIN":
                    # Admin sees all reports in their organization
                    reports_query = reports_query.join(
                        User, ProjectDetail.user_id == User.id
                    ).filter(User.organization_id == organization_id)
                    logger.info("[WorkspaceService] Applied ADMIN filter - all reports in organization")

                elif role_upper == "MANAGER":
                    # Manager sees reports for projects assigned to them or their subordinates
                    subordinate_ids = session.query(User.id).filter(User.manager_id == session_user.id).all()
                    subordinate_ids = [sid[0] for sid in subordinate_ids]
                    logger.info(f"[WorkspaceService] Manager subordinate IDs: {subordinate_ids}")
                    print(f"[DEBUG] Manager subordinate IDs: {subordinate_ids}")

                    if subordinate_ids:
                        reports_query = reports_query.filter(
                            or_(
                                ProjectDetail.assigned_to == session_user.id,
                                ProjectDetail.assigned_to.in_(subordinate_ids)
                            )
                        )
                    else:
                        reports_query = reports_query.filter(ProjectDetail.assigned_to == session_user.id)

                    # Also filter by organization
                    reports_query = reports_query.join(
                        User, ProjectDetail.user_id == User.id
                    ).filter(User.organization_id == organization_id)
                    logger.info("[WorkspaceService] Applied MANAGER filter")

                elif role_upper == "DEVELOPER":
                    # Developer sees only reports for projects assigned to them
                    reports_query = reports_query.filter(
                        ProjectDetail.assigned_to == session_user.id
                    ).join(
                        User, ProjectDetail.user_id == User.id
                    ).filter(User.organization_id == organization_id)
                    logger.info("[WorkspaceService] Applied DEVELOPER filter")

                else:
                    logger.error(f"[WorkspaceService] Unknown role: {role_name}")
                    return ServiceResponse.failure(f"Unknown user role: {role_name}", 400)

                # Execute query
                reports = reports_query.all()
                logger.info(f"[WorkspaceService] Found {len(reports)} reports")
                print(f"[DEBUG] Found {len(reports)} reports")

                # Format response according to requirements
                report_data = []
                for report in reports:
                    try:
                        report_item = {
                            "report_id": str(report.id),  # Use report.id instead of report.report_id
                            "report_name": report.name,
                            "project_name": report.project.name if report.project else None,
                            "is_analyzed": report.is_analyzed,
                            "is_converted": report.is_converted,
                            "is_migrated": report.is_migrated,
                            "analyzed_status": report.analyzed_status.value if report.analyzed_status else None,
                            "converted_status": report.converted_status.value if report.converted_status else None,
                            "migrated_status": report.migrated_status.value if report.migrated_status else None,
                        }
                        report_data.append(report_item)
                    except Exception as e:
                        logger.error(f"[WorkspaceService] Error formatting report {report.id}: {e}")
                        print(f"[ERROR] Error formatting report {report.id}: {e}")
                        continue

                logger.info(f"[WorkspaceService] Successfully formatted {len(report_data)} reports")
                return ServiceResponse.success(report_data)

            except Exception as e:
                logger.error(f"[WorkspaceService] Error in get_reports_by_user_role: {e}", exc_info=True)
                print(f"[ERROR] Exception in WorkspaceService: {e}")
                return ServiceResponse.failure(f"Error fetching reports: {str(e)}", 500)