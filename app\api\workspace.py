from fastapi import <PERSON><PERSON>out<PERSON>, Depends, Path
from fastapi.responses import JSONResponse

from app.models.users import User
from app.core.dependencies import get_current_user
from app.core.response import ServiceResponse
from app.core.logger_setup import logger
from app.services.workspace import WorkspaceProcessor
from app.schemas.workspace import ReportStatusUpdateRequest, SuccessResponse


workspace_router = APIRouter()

@workspace_router.get("/workspace/reports", response_model=dict)
async def get_workspace_reports(current_user: User = Depends(get_current_user)):
    try:
        logger.info(f"[WORKSPACE_API] Endpoint hit: /workspace/reports by user {current_user.id}")
        print(f"[DEBUG] Workspace endpoint called by user: {current_user.email}")
        print(f"[DEBUG] User ID: {current_user.id}")
        print(f"[DEBUG] User type: {type(current_user)}")

        response = WorkspaceProcessor.process_get_reports_by_user_role(current_user)
        logger.info(f"[WORKSPACE_API] Response from processor: success={response.success}, status={response.status_code}")
        print(f"[DEBUG] Processor response: success={response.success}, data_count={len(response.data) if response.data else 0}")

        return JSONResponse(content={"data": response.data, "error": response.error}, status_code=response.status_code)
    except Exception as e:
        logger.error(f"[WORKSPACE_API] Exception in workspace endpoint: {e}", exc_info=True)
        print(f"[ERROR] Exception in workspace endpoint: {e}")
        import traceback
        traceback.print_exc()
        return JSONResponse(content={"data": None, "error": "Internal server error"}, status_code=500)
    

@workspace_router.patch(
    "/workspace/reports/{report_id}/status",
    response_model=SuccessResponse,
    summary="Update report status flags (unit_tested, uat_tested, deployed)"
)
def update_report_status(
    report_id: str = Path(..., description="ID of the report to update"),
    status_update: ReportStatusUpdateRequest = ...,  # This will be your request body
    user: User = Depends(get_current_user)
):
    """
    Update the `unit_tested`, `uat_tested`, and `deployed` flags for a report.
    """
    result = WorkspaceProcessor.process_update_report_status(report_id, status_update, user)
    if not result.success:
        raise HTTPException(status_code=400, detail=result.error)
    return result