from fastapi import APIRouter, Depends
from fastapi.responses import JSONResponse
from app.models.users import User
from app.core.dependencies import get_current_user
from app.services.workspace import WorkspaceProcessor
from app.core.logger_setup import logger

workspace_router = APIRouter()

# @workspace_router.get("/workspace/reports", response_model=dict)
# async def get_my_workspace_reports(user: User = Depends(get_current_user)):
#     """
#     API to fetch reports accessible to the current user based on their role.
#     This forms the data for the 'My Workspace' screen.
#     """
#     response = WorkspaceProcessor.process_get_reports_by_user_role(user=user)
#     return JSONResponse(content={"data": response.data, "error": response.error}, status_code=response.status_code)

@workspace_router.get("/workspace/reports", response_model=dict)
async def get_workspace_reports(current_user: User = Depends(get_current_user)):
    logger.info(f"Endpoint hit: /workspace/reports by user {current_user.id}")
    print(f"[DEBUG] Endpoint called by user: {current_user.email}")
    
    response = WorkspaceProcessor.process_get_reports_by_user_role(current_user)
    logger.info(f"Response from processor: {response}")
    return JSONResponse(content={"data": response.data, "error": response.error}, status_code=response.status_code)