from fastapi import APIRouter, Depends, Query
from fastapi.responses import JSONResponse

from app.schemas import *
from app.services import DiscoverService, DiscoverServerService
from app.models_old.user import UserOld
from app.models.users import User
from app.core.dependencies import get_current_user
from uuid import UUID
from app.services.discovery import DiscoverProcessor
discover_router = APIRouter()

@discover_router.post("/discover/server", response_model=dict)
async def discover_server(request: DiscoverServerRequest, page: int = Query(1, ge=1), page_size: int = Query(10, ge=1, le=100),
                          user: UserOld = Depends(get_current_user)):
    """API to get a discover server."""
    response = await DiscoverServerService.execute(DiscoverServerService()._discover_server, request, page, page_size)
    return JSONResponse(content={"data": response.data, "error": response.error}, status_code=response.status_code)

@discover_router.post("/discover/servers", response_model=dict)
async def get_all_servers(organization_id: UUID = Query(...),page: int = Query(1),page_size: int = Query(10),
                             user: User = Depends(get_current_user)):

    """API to get all servers for a given organization."""
    response = DiscoverProcessor().process_get_all_servers(organization_id, page, page_size)
    return JSONResponse(content={"data": response.data, "error": response.error},status_code=response.status_code)
