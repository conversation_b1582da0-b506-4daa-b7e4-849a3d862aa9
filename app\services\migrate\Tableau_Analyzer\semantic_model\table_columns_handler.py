import xml.etree.ElementTree as ET
import uuid

def fetch_objects_data(datasource):
    objects = datasource.findall('.//object')
    objects_data = {}
    for obj in objects:
        caption = obj.get('caption')
        object_id = obj.get('id')
        if caption and object_id:
            objects_data[object_id] = caption
    return objects_data

def process_datasources(twb_file_path):
    tree = ET.parse(twb_file_path)
    root = tree.getroot()
    datasources = root.find(".//datasources").findall(".//datasource")
    overall_datasources = {}
    for datasource in datasources:
        tables_data = {}
        datasource_name = datasource.get('name')
        if datasource_name == "Parameters":
            continue
        metadata_records = datasource.findall(".//metadata-record[@class='column']")
        objects_data = fetch_objects_data(datasource)
        overall_datasources.setdefault(datasource_name, {})
        for record in metadata_records:
            remote_name = record.findtext('remote-name')
            local_type = record.findtext('local-type')
            aggregation = record.findtext('aggregation')
            parent_name = record.findtext('parent-name').strip("[]")

            object_id = next((elem.text for elem in record if elem.tag.endswith("object-id")), None)
            object_id = object_id.strip("[]") if object_id else None

            table_name = objects_data.get(object_id) 
            table_name = table_name if table_name else parent_name

            tables_data.setdefault(table_name, {})
            tables_data[table_name].setdefault("unique_id", str(uuid.uuid4()))
            tables_data[table_name].setdefault("column_data", [])
            tables_data[table_name]["column_data"].append({
                'data_type': local_type,
                'aggregation': aggregation,
                'column_name': remote_name,
                'unique_id': str(uuid.uuid4()),
            })
        
        overall_datasources[datasource_name] = tables_data
    return overall_datasources
    
