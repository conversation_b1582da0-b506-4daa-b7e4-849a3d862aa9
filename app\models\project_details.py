# import uuid
# from sqlalchemy import Column, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, text
# from sqlalchemy.dialects.postgresql import UUID
# from sqlalchemy.orm import relationship
# from app.core.session import Base
# from app.models.base import AuditMixin

# class ProjectDetail(Base, AuditMixin):
#     __tablename__ = "project_details"
#     __table_args__ = {"schema": "biport_dev"}

#     id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
#     name = Column(String, nullable=False)
#     is_upload = Column(Boolean, server_default=text("false"), nullable=False)

#     site_id = Column(UUID(as_uuid=True), ForeignKey("biport_dev.tableau_site_details.id"), nullable=False)
#     server_id = Column(UUID(as_uuid=True), ForeignKey("biport_dev.tableau_server_details.id"), nullable=False)
#     parent_id = Column(UUID(as_uuid=True), Foreign<PERSON>ey("biport_dev.project_details.id"), nullable=True)
#     user_id = Column(UUID(as_uuid=True), ForeignKey("biport_dev.users.id"), nullable=False)
#     assigned_to = Column(UUID(as_uuid=True), ForeignKey("biport_dev.users.id"), nullable=True)
#     # Relationships
#     creator = relationship("User", foreign_keys=[user_id], backref="created_projects")
#     assignee = relationship("User", foreign_keys=[assigned_to], backref="assigned_projects") 

import uuid
from sqlalchemy import Column, String, Boolean, ForeignKey, text, or_
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship, Query, joinedload
from app.core.session import Base
from app.models.base import AuditMixin
from app.models.users import User

class ProjectDetail(Base, AuditMixin):
    __tablename__ = "project_details"
    __table_args__ = {"schema": "biport_dev"}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String, nullable=False)
    is_upload = Column(Boolean, server_default=text("false"), nullable=False)

    site_id = Column(UUID(as_uuid=True), ForeignKey("biport_dev.tableau_site_details.id"), nullable=False)
    server_id = Column(UUID(as_uuid=True), ForeignKey("biport_dev.tableau_server_details.id"), nullable=False)
    parent_id = Column(UUID(as_uuid=True), ForeignKey("biport_dev.project_details.id"), nullable=True)
    user_id = Column(UUID(as_uuid=True), ForeignKey("biport_dev.users.id"), nullable=False)
    assigned_to = Column(UUID(as_uuid=True), ForeignKey("biport_dev.users.id"), nullable=True)

    creator = relationship("User", foreign_keys=[user_id], back_populates="created_projects")
    assignee = relationship("User", foreign_keys=[assigned_to], back_populates="assigned_projects")
    reports = relationship("ReportDetail", back_populates="project")

    @classmethod
    def get_projects_by_user_role(cls, session, user, role_name: str) -> Query:
        """
        Return a SQLAlchemy query for projects based on user's role and organization.
        """
        query = session.query(cls).options(joinedload(cls.reports))

        # Filter by organization
        query = query.join(cls.creator).filter(User.organization_id == user.organization_id)

        # Role-based access control
        if role_name == "Admin":
            pass  # See all
        elif role_name == "Manager":
            subordinate_ids = session.query(User.id).filter(User.manager_id == user.id).all()
            subordinate_ids = [sid[0] for sid in subordinate_ids]
            query = query.filter(
                or_(
                    cls.assigned_to == user.id,
                    cls.assigned_to.in_(subordinate_ids)
                )
            )
        elif role_name == "Developer":
            query = query.filter(cls.assigned_to == user.id)

        return query