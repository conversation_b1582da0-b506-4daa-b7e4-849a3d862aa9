import uuid
from app.core.session import scoped_context
from sqlalchemy import Column, String, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from app.core.session import scoped_context,Base
from typing import Optional
from app.models.base import AuditMixin
from app.core.exceptions import AuthenticationError
from passlib.hash import bcrypt

class User(Base, AuditMixin):
    __tablename__ = "users"
    __table_args__ = {"schema": "biport_dev"}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String, nullable=False)
    email = Column(String, nullable=False, unique=True)
    password_hash = Column(String, nullable=False)
    phone_number = Column(String, nullable=False, unique=True)

    organization_id = Column(UUID(as_uuid=True), ForeignKey("biport_dev.organization_details.id"), nullable=False)
    role_id = Column(UUID(as_uuid=True), ForeignKey("biport_dev.roles.id"), nullable=False)
    manager_id = Column(UUID(as_uuid=True), ForeignKey("biport_dev.users.id"), nullable=True)

    # Relationships
    organization = relationship("OrganizationDetail", back_populates="users")
    role = relationship("Role")
    manager = relationship("User", remote_side="User.id", backref="subordinates")

    # Project relationships
    created_projects = relationship("ProjectDetail", foreign_keys="ProjectDetail.user_id", back_populates="creator")
    assigned_projects = relationship("ProjectDetail", foreign_keys="ProjectDetail.assigned_to", back_populates="assignee")

class UserManager:
    def __init__(
            self,
            name: str, 
            email: str, 
            password: str, 
            phone_number: str, 
            organization_id: UUID, 
            role_id: UUID, 
            manager_id: UUID
        ):
        self.name = name
        self.email = email
        self.password = password
        self.phone_number = phone_number
        self.organization_id = organization_id
        self.role_id = role_id
        self.manager_id = manager_id

    
    @staticmethod
    def get_user_by_email(email: str, load_role: bool = False) -> Optional[User]:
        """Fetch a user by their email (case-insensitive)."""
        with scoped_context() as session:
            query = session.query(User).filter(User.email.ilike(email))
            if load_role:
                from sqlalchemy.orm import joinedload
                query = query.options(joinedload(User.role), joinedload(User.organization))
            return query.first()

    @staticmethod
    def get_user_by_id_with_relations(user_id: str, session) -> Optional[User]:
        """Fetch a user by ID with role and organization loaded in the given session."""
        from sqlalchemy.orm import joinedload
        return session.query(User).options(
            joinedload(User.role),
            joinedload(User.organization)
        ).filter(User.id == user_id).first()
    
    @staticmethod
    def login_user(email: str, password: str) -> Optional[User]:
        user = UserManager.get_user_by_email(email)
        if user is None:
            raise AuthenticationError("User not found.")
        if bcrypt.verify(password, user.password_hash):
            return user
        raise AuthenticationError("Incorrect password. Please try again.")

    @staticmethod
    def check_exists_email_mobile(data):
        with scoped_context() as session:
            email = data.email
            phone_number = data.phone_number
            return session.query(User).filter(
                (User.phone_number == phone_number) | (User.email.ilike(email))
            ).first()

    @staticmethod
    def add_user(data):
        with scoped_context() as session:
            user = User(
                id=uuid.uuid4(),
                name=data.name,
                email=data.email,
                password_hash=bcrypt.hash(data.password),
                phone_number=data.phone_number,
                organization_id=uuid.UUID(data.organization_id),
                role_id=uuid.UUID(data.role_id),
                manager_id=uuid.UUID(data.manager_id) if data.manager_id else None
            )
            session.add(user)
            session.commit()
            session.refresh(user)
            return user
